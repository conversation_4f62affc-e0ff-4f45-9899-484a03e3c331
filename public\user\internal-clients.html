<!DOCTYPE html>

<html>
  <head>
    <meta charset="utf-8" />
    <title>Internal Clients Registration Form</title>
    <!-- Include navbar CSS -->
    <link rel="stylesheet" href="/utils/navbar.css">
    <!-- Include modal CSS -->
    <link rel="stylesheet" href="/utils/modals.css">
    <style>
      @page {
        size: 8.5in 13in;
        margin-left: 1cm;
        margin-right: 1cm;
        margin-top: 1cm;
        margin-bottom: 1cm;
      }
      @media print {
        .form-page {
          display: block !important;
        }

        .navbar,
        .footer,
        .navigation {
          display: none !important;
        }
      }

      body {
        font-family: Century Gothic, sans-serif;
        font-size: 11px;
        margin: 0;
        padding: 0;
      }

      /* Time Conflict Warning Styles */
      .time-conflict-warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .conflict-message {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #856404;
        font-weight: 500;
        margin-bottom: 1rem;
      }

      .conflict-message i {
        color: #f39c12;
        font-size: 1.2rem;
      }

      .suggested-times h4 {
        color: #333;
        margin-bottom: 0.5rem;
        font-size: 1rem;
      }

      .time-suggestions-list {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
      }

      .time-suggestion {
        background: #e8f5e8;
        border: 1px solid #4caf50;
        border-radius: 6px;
        padding: 0.5rem 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9rem;
        color: #2e7d32;
      }

      .time-suggestion:hover {
        background: #4caf50;
        color: white;
        transform: translateY(-1px);
      }

      /* Ensure navbar uses proper font from navbar.css */
      .navbar,
      .navbar * {
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      }
      .footer {
        background-color: #033e2e;
        color: white;
        text-align: center;
        padding: 20px;
        font-size: 14px;
      }
      .form-container {
        width: 100%;
        padding: 10px;
        box-sizing: border-box;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 10px;
      }
      td,
      th {
        border: 1px solid black;
        padding: 5px;
        vertical-align: top;
      }
      .no-border {
        border: none !important;
      }
      .form-title {
        background-color: #3535354d;
        text-align: center;
        font-weight: bold;
        padding: 8px;
        margin-bottom: 10px;
        border: 2px solid black;
        border-radius: 7px;
      }
      .two-columns {
        display: flex;
        gap: 10px;
        padding: 0;
        box-sizing: border-box;
      }
      .two-columns > div {
        flex: 1;
      }
      .two-columns table {
        width: 100%;
      }
      .page-break {
        page-break-before: always;
      }
      .section-title {
        font-weight: bold;
        background-color: #747272;
      }
      .small {
        font-size: 10px;
      }
      .header-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
      }
      .header-row img {
        height: 40px;
      }
      .form-page {
        display: none;
      }
      .form-page.active {
        display: block;
      }
      .navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
      }
      button {
        padding: 6px 12px;
        font-size: 14px;
      }
      .outer-border {
        border: 1px solid black;
        border-collapse: separate;
        border-spacing: 0;
      }

      .outer-border td,
      .outer-border th {
        border: none;
        padding: 8px;
      }
      .no-border {
        border-collapse: collapse;
        border: none;
      }
      .no-border td,
      .no-border th {
        border: none;
        padding: 5px;
      }

      .full-form {
        max-width: 1200px;
        margin: 2rem auto;
        font-family: system-ui, sans-serif;
        background-color: #ffffff;
        color: #1f1f1f;
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid #e0e0e0;
      }

      .full-form label {
        display: block;
        margin-bottom: 1.25rem;
        color: #2a4d2e;
        font-weight: 600;
        font-size: 1rem;
      }

      .full-form input,
      .full-form select,
      .full-form textarea {
        width: 100%;
        padding: 0.75rem;
        font-size: 1rem;
        margin-top: 0.25rem;
        box-sizing: border-box;
        color: #1a1a1a;
        background-color: #f5f5f5;
        border: 1px solid #ccc;
        border-radius: 4px;
      }

      .full-form input:focus,
      .full-form select:focus,
      .full-form textarea:focus {
        border-color: #4a7d4a;
        background-color: #f0f8f1;
        outline: none;
      }

      .full-form button {
        background: linear-gradient(135deg, #2e7d32, #1b5e20);
        color: white;
        border: none;
        padding: 0.875rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 8px;
        cursor: pointer;
        margin-top: 2rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
        display: flex;
        align-items: center;
        gap: 8px;
        justify-content: center;
        min-width: 160px;
      }

      .full-form button:hover {
        background: linear-gradient(135deg, #1b5e20, #0d4e14);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(46, 125, 50, 0.4);
      }

      .full-form button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
        transform: none;
      }

      .full-form input[type="checkbox"],
      .full-form input[type="radio"] {
        vertical-align: middle;
        transform: translateY(-1px);
        margin-right: 0.5rem;
        width: auto;
      }

      .step {
        display: none;
        animation: fade 0.25s linear forwards;
      }
      .step.active {
        display: block;
      }

      @keyframes fade {
        from {
          opacity: 0;
          transform: translateY(8px);
        }
        to {
          opacity: 1;
          transform: none;
        }
      }

      @media screen {
        #internalForm,
        #combinedForm {
          display: none;
        }
      }
      @media print {
        #userForm {
          display: none !important;
        }
        .form-page,
        #combinedForm {
          display: block !important;
        }
      }

      fieldset {
        border: 2px solid #e8f5e8;
        padding: 1.5rem;
        margin-bottom: 2rem;
        background-color: #f8fdf8;
        border-radius: 8px;
        transition: border-color 0.3s ease;
      }

      fieldset:hover {
        border-color: #c8e6c9;
      }

      legend {
        color: #2e7d32;
        font-weight: 700;
        font-size: 1.1rem;
        padding: 0 0.5rem;
        background-color: white;
        border-radius: 4px;
      }

      fieldset label {
        color: #000000;
        font-weight: normal;
        display: inline-block;
        margin-right: 1.5rem;
      }
    </style>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>UC Campus</title>
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css"
      rel="stylesheet"
    />
  </head>

  <script>
    function onlyOne(checkbox, groupName) {
      const groupCheckboxes = document.querySelectorAll(
        `input[name="${groupName}"]`
      );
      groupCheckboxes.forEach((cb) => {
        if (cb !== checkbox) cb.checked = false;
      });
    }

    function toggleLongTablesCount(checkbox) {
      const countBox = document.getElementById("u_long_tables_count");
      if (checkbox.value === "yes") {
        countBox.disabled = !checkbox.checked;
      } else if (checkbox.value === "no" && checkbox.checked) {
        countBox.disabled = true;
        countBox.value = "";
      }
    }
  </script>

  <script>
    document.addEventListener("DOMContentLoaded", () => {
      const venueField = document.getElementById("u_venue");
      const attendField = document.getElementById("u_attend");
      const maxNote = document.getElementById("attend_max_note");

      const capacityMap = {
        "Auditorium": 250,
        "Cañao Hall": 150,
        "Conference Room": 20,
        "Gymnasium": 1200,
        "Laboratory Room": 100,
        "Lecture Room (Main)": 60,
        "Training Center": 40,

        "AVR": 45,
        "Crime Lab": 50,
        "Deftac": 50,
        "Lecture Room (Legarda)": 45,
        "Moot Court": 45,
      };

      let currentMax = 9999;

      venueField.addEventListener("change", () => {
        const selected = venueField.value;
        currentMax = capacityMap[selected] || 9999;
        attendField.max = currentMax;
        attendField.placeholder = `Max ${currentMax}`;
        attendField.title = `Maximum allowed for ${selected}: ${currentMax}`;
        if (maxNote) maxNote.textContent = `Max: ${currentMax}`;
      });

      attendField.addEventListener("input", () => {
        const value = parseInt(attendField.value, 10);
        if (value > currentMax) {
          alert(`This venue allows only up to ${currentMax} attendees.`);
          attendField.value = currentMax;
        }
      });
    });
  </script>

  <body>
    <!-- Navbar container for utils -->
    <div id="navbar-container"></div>

    <form id="userForm" class="full-form">
      <input type="hidden" id="curfew_required" name="curfew_required" value="No" />
      <div style="text-align: center; margin-bottom: 2rem; padding-bottom: 1.5rem; border-bottom: 2px solid #e8f5e8;">
        <div style="display: flex; align-items: center; justify-content: center; gap: 1rem; margin-bottom: 1rem;">
          <img src="https://upload.wikimedia.org/wikipedia/commons/8/84/UC_Official_Logo.png" alt="UC Logo" style="height: 60px; width: auto;">
          <div>
            <h1 style="margin: 0; color: #2e7d32; font-size: 1.8rem; font-weight: 700;">Internal Clients Request Form</h1>
            <p style="margin: 0.5rem 0 0 0; color: #666; font-size: 1rem;">Facilities Management Operations Unit</p>
          </div>
        </div>
        <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 1rem; margin-top: 1rem;">
          <i class="fas fa-info-circle" style="color: #856404; margin-right: 0.5rem;"></i>
          <strong style="color: #856404;">NOTE:</strong>
          <span style="color: #856404;">Submit two (2) copies of this form at least two (2) weeks prior to your event.</span>
        </div>
      </div>

      <label style="display: block; font-size: 0.95rem; text-align: center">
        Upload Request Letter (PDF only):
        <input
          type="file"
          name="request_letter"
          accept="application/pdf"
          style="margin-top: 0.25rem; text-align: center; width: 25%"
        />
        <small
          style="
            display: block;
            margin-top: 0.25rem;
            color: #666;
            font-size: 0.75rem;
            line-height: 1.2;
          "
        >
          Please attach a signed request letter from your department.<br />
          Coordinate with your department to obtain it.
        </small>
      </label>

      <div style="background-color: #f8fdf8; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem; border-left: 4px solid #2e7d32;">
        <h3 style="margin: 0 0 1.5rem 0; color: #2e7d32; font-size: 1.2rem; font-weight: 600;">
          <i class="fas fa-building" style="margin-right: 0.5rem;"></i>
          Organization Information
        </h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem">
          <label>
            College / Office / Department
            <input
              type="text"
              id="u_org"
              name="u_org"
              data-target="organization_name"
              required
              style="width: 100%"
            />
          </label>

          <label>
            Contact Number / Email
            <input
              type="text"
              name="u_contact"
              data-target="contact"
              required
              style="width: 100%"
            />
          </label>
        </div>
      </div>

      <div style="background-color: #f8fdf8; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem; border-left: 4px solid #2e7d32;">
        <h3 style="margin: 0 0 1.5rem 0; color: #2e7d32; font-size: 1.2rem; font-weight: 600;">
          <i class="fas fa-calendar-alt" style="margin-right: 0.5rem;"></i>
          Event Details
        </h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
          <label>
            Function / Event
            <input
              type="text"
              id="u_event"
              name="u_event"
              data-target="event_name"
              required
              style="width: 100%"
            />
          </label>

          <label>
            Requested Venue / Room
            <select
              id="u_venue"
              name="u_venue"
              data-target="venue"
              required
              style="width: 100%"
            >
              <option value="">— Select —</option>

              <!-- Main Campus -->
              <optgroup label="Main Campus">
                <option value="Auditorium">Auditorium</option>
                <option value="Cañao Hall">Cañao Hall</option>
                <option value="Conference Room">Conference Room</option>
                <option value="Gymnasium">Gymnasium</option>
                <option value="Laboratory Room">Laboratory Room</option>
                <option value="Lecture Room (Main)">Lecture Room (Main)</option>
                <option value="Training Center">Training Center</option>
              </optgroup>

              <option disabled>──────────</option>

              <!-- Legarda -->
              <optgroup label="Legarda">
                <option value="AVR">AVR</option>
                <option value="Crime Lab">Crime Lab</option>
                <option value="Deftac">Deftac</option>
                <option value="Lecture Room (Legarda)">
                  Lecture Room (Legarda)
                </option>
                <option value="Moot Court">Moot Court</option>
              </optgroup>
            </select>
          </label>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
          <label>
            Number of Attendees
            <input
              type="number"
              id="u_attend"
              name="u_attend"
              data-target="attendees"
              min="1"
              required
              style="width: 100%"
            />
            <small id="attend_max_note" style="margin-left: 5px; color: #666;">Max: —</small>
          </label>

          <label>
            Scope of Activity
            <input type="text" name="u_scope" data-target="audience_type" />
          </label>
        </div>
      </div>
      <div style="background-color: #f8fdf8; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem; border-left: 4px solid #2e7d32;">
        <h3 style="margin: 0 0 1.5rem 0; color: #2e7d32; font-size: 1.2rem; font-weight: 600;">
          <i class="fas fa-clock" style="margin-right: 0.5rem;"></i>
          Schedule & Budget Information
        </h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
          <label>
            Day / Date of Event
            <input
              type="date"
              id="u_date"
              name="u_date"
              data-target="event_date"
              required
              readonly
              style="background-color: #f0f0f0; cursor: not-allowed;"
            />
            <small style="color: #666; font-size: 0.8rem; margin-top: 0.25rem; display: block;">
              Date selected from reservation calendar
            </small>
          </label>

          <label>
            Time Start
            <input
              type="time"
              id="u_start"
              name="u_start"
              data-target="time_start"
              required
            />
          </label>

          <label>
            Time End
            <input
              type="time"
              id="u_end"
              name="u_end"
              data-target="time_end"
              required
            />
          </label>
        </div>

        <!-- Time Conflict Warning and Suggestions -->
        <div id="time-conflict-warning" class="time-conflict-warning" style="display: none;">
          <div class="conflict-message">
            <i class="fas fa-exclamation-triangle"></i>
            <span>Time conflict detected! The selected time overlaps with existing reservations.</span>
          </div>
          <div id="suggested-times" class="suggested-times">
            <h4>Suggested Available Times:</h4>
            <div id="time-suggestions" class="time-suggestions-list"></div>
          </div>
        </div>

        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem;">
          <label>
            Budget Source
            <input type="text" name="u_budget" data-target="budget_source" />
          </label>

          <label>
            Admission Fee
            <input type="text" name="u_fee" data-target="admission_fee" />
          </label>

          <label>
            Other Fees (please indicate)
            <input type="text" name="u_other_fees" data-target="other_fees" />
          </label>
        </div>
      </div>

      <div style="background-color: #f8fdf8; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem; border-left: 4px solid #2e7d32;">
        <h3 style="margin: 0 0 1.5rem 0; color: #2e7d32; font-size: 1.2rem; font-weight: 600;">
          <i class="fas fa-concierge-bell" style="margin-right: 0.5rem;"></i>
          Additional Services
        </h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1rem">
          <fieldset>
            <legend>With Food / Canteen Service?</legend>

            <label>
              <input
                type="checkbox"
                name="u_food"
                value="yes"
                onclick="onlyOne(this, 'u_food')"
                data-target="food_service"
                data-value="yes"
              />
              Yes
            </label>

            <label>
              <input
                type="checkbox"
                name="u_food"
                value="no"
                onclick="onlyOne(this, 'u_food')"
                data-target="food_service"
                data-value="no"
              />
              No
            </label>
          </fieldset>

          <fieldset>
            <legend>Medical Team Needed?</legend>
            <label>
              <input
                type="checkbox"
                name="u_medical"
                value="yes"
                onclick="onlyOne(this, 'u_medical')"
                data-target="medical_team"
                data-value="yes"
              />
              Yes
            </label>
            <label>
              <input
                type="checkbox"
                name="u_medical"
                value="no"
                onclick="onlyOne(this, 'u_medical')"
                data-target="medical_team"
                data-value="no"
              />
              No
            </label>
          </fieldset>

          <fieldset>
            <legend>Security Guards Needed?</legend>
            <label>
              <input
                type="checkbox"
                name="u_security"
                value="yes"
                onclick="onlyOne(this, 'u_security')"
                data-target="security_guards"
                data-value="yes"
              />
              Yes
            </label>
            <label>
              <input
                type="checkbox"
                name="u_security"
                value="no"
                onclick="onlyOne(this, 'u_security')"
                data-target="security_guards"
                data-value="no"
              />
              No
            </label>
          </fieldset>
        </div>
      </div>
      <div style="background-color: #f8fdf8; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem; border-left: 4px solid #2e7d32;">
        <h3 style="margin: 0 0 1.5rem 0; color: #2e7d32; font-size: 1.2rem; font-weight: 600;">
          <i class="fas fa-tools" style="margin-right: 0.5rem;"></i>
          Equipment & Requirements
        </h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem">
          <fieldset>
            <legend>Audio-Visual Requirements</legend>
          <label
            ><input
              type="checkbox"
              name="mic"
              value="Microphones"
              data-target="mic_checkbox"
            />
            Microphones</label
          >
          <label
            ><input
              type="checkbox"
              name="mic_stand"
              value="Mic_Stands"
              data-target="mic_stands_checkbox"
            />
            Microphone Stands</label
          >
          <label
            ><input
              type="checkbox"
              name="projector"
              value="Projector"
              data-target="projector_checkbox"
            />
            Projector</label
          >
          <label
            ><input
              type="checkbox"
              name="pa_system"
              value="PA_System"
              data-target="pa_system_checkbox"
            />
            PA System</label
          >
          <label
            ><input
              type="checkbox"
              name="laptop"
              value="Laptop"
              data-target="laptop_checkbox"
            />
            Laptop</label
          >
          <label
            ><input
              type="checkbox"
              name="lights"
              value="Lights"
              data-target="lights_checkbox"
            />
            Lights</label
          >
          <label
            ><input
              type="checkbox"
              name="wifi_connection"
              value="WiFi_Connection"
              data-target="wifi_checkbox"
            />
            WiFi Connection</label
          >
          <label
            ><input
              type="checkbox"
              name="podium_monitor"
              value="Podium_Monitor"
              data-target="podium_checkbox"
            />
            Podium Monitor Board</label
          >
          <label
            ><input
              type="checkbox"
              name="live_streaming"
              value="Live_Streaming"
              data-target="stream_checkbox"
            />
            Live Streaming</label
          >

          <label
            >Other AV needs:
            <input type="text" name="u_av_other" data-target="others_AV" />
          </label>
        </fieldset>

        <fieldset>
          <legend>Property / Equipment Needs (from LMO)</legend>

          <!-- Air Cooler -->
          <label>Air Cooler:</label><br />
          <label>
            <input
              type="checkbox"
              name="u_air_cooler"
              value="yes"
              onclick="onlyOne(this, 'u_air_cooler')"
              data-target="air_cooler"
              data-value="yes"
            />
            Yes
          </label>
          <label>
            <input
              type="checkbox"
              name="u_air_cooler"
              value="no"
              onclick="onlyOne(this, 'u_air_cooler')"
              data-target="air_cooler"
              data-value="no"
            />
            No
          </label>

          <br /><br />

          <!-- Long Tables -->
          <fieldset>
            <legend>Long Tables:</legend>

            <label>
              <input
                type="checkbox"
                name="u_long_tables"
                value="yes"
                onclick="onlyOne(this, 'u_long_tables'); toggleLongTablesCount(this);"
                data-target="long_tables"
                data-value="yes"
              />
              Yes </label
            ><br />

            <label>
              <input
                type="checkbox"
                name="u_long_tables"
                value="no"
                onclick="onlyOne(this, 'u_long_tables'); toggleLongTablesCount(this);"
                data-target="long_tables"
                data-value="no"
              />
              No </label
            ><br /><br />

            <label>
              If Yes, how many?
              <input
                type="number"
                name="u_long_tables_count"
                id="u_long_tables_count"
                data-target="long_tables_count"
                min="0"
                style="width: 150px"
                disabled
              />
            </label>
          </fieldset>

          <br /><br />

          <!-- Flags -->
          <label>Philippine Flag and UC Flag:</label><br />
          <label>
            <input
              type="checkbox"
              name="u_flags"
              value="yes"
              onclick="onlyOne(this, 'u_flags')"
              data-target="flags"
              data-value="yes"
            />
            Yes
          </label>
          <label>
            <input
              type="checkbox"
              name="u_flags"
              value="no"
              onclick="onlyOne(this, 'u_flags')"
              data-target="flags"
              data-value="no"
            />
            No
          </label>
        </fieldset>
        </div>
      </div>

      <div id="exemption-section" style="display: none; margin-top: 2rem; padding: 2rem; background-color: #fff3cd; border: 2px solid #ffc107; border-radius: 8px;">
        <div style="text-align: center; margin-bottom: 2rem;">
          <h2 style="color: #856404; margin: 0;">📋 Page 2: Curfew Exemption Form</h2>
          <p style="color: #856404; margin: 0.5rem 0;">This form will be included in your PDF submission</p>
        </div>

        <div style="background-color: #f8fdf8; padding: 1.5rem; border-radius: 8px; margin-bottom: 2rem; border-left: 4px solid #2e7d32;">
          <h3 style="margin: 0 0 1.5rem 0; color: #2e7d32; font-size: 1.2rem; font-weight: 600;">
            <i class="fas fa-clock" style="margin-right: 0.5rem;"></i>
            REQUEST FOR EXEMPTION FROM CURFEW
          </h3>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
            <label>
              Requesting Office
              <input type="text" name="exemption_office" id="exemption_office" style="width: 100%;" />
            </label>

            <label>
              Venue
              <input type="text" name="exemption_venue" id="exemption_venue" style="width: 100%;" />
            </label>
          </div>

          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
            <label>
              Number of People
              <input type="number" name="exemption_people" id="exemption_people" min="1" style="width: 100%;" />
            </label>

            <label>
              Activity Date
              <input type="date" name="exemption_date" id="exemption_date" style="width: 100%;" />
            </label>
          </div>

          <label style="margin-bottom: 1rem;">
            Title/Nature of Activity
            <input type="text" name="exemption_activity" id="exemption_activity" style="width: 100%;" />
          </label>

          <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 1rem; align-items: end; margin-bottom: 1rem;">
            <label>
              Time Start
              <input type="time" name="exemption_start" id="exemption_start" style="width: 100%;" />
            </label>
            <span style="text-align: center; padding: 0 1rem;">to</span>
            <label>
              Time End
              <input type="time" name="exemption_end" id="exemption_end" style="width: 100%;" />
            </label>
          </div>

          <div style="background-color: #e8f5e8; padding: 1rem; border-radius: 4px; margin-bottom: 1rem;">
            <h4 style="margin: 0 0 0.5rem 0; color: #2e7d32;">Attachment: Duly accomplished and approved</h4>
            <label style="display: block; margin-bottom: 0.5rem;">
              <input type="checkbox" checked disabled /> Facilities & Service Request Form for Internal Clients (UC-SDWO-FORM-22)
            </label>
            <label style="display: block; margin-bottom: 0.5rem;">
              <input type="checkbox" disabled /> Student Activity Request Form (UC-SDWO-FORM-106)
            </label>
            <label style="display: block;">
              <input type="checkbox" disabled /> List of Participants
            </label>
          </div>

          <div style="text-align: center; margin-top: 2rem;">
            <button type="button" onclick="proceedWithSubmission()" style="background-color: #2e7d32; color: white; padding: 0.75rem 2rem; border: none; border-radius: 4px; font-size: 1rem; cursor: pointer;">
              <i class="fas fa-check"></i> Confirm & Submit Both Forms
            </button>
          </div>
        </div>
      </div>

      <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 2px solid #e8f5e8;">
        <button type="button" id="preparePrint" onclick="handleFormSubmission()">
          <i class="fas fa-paper-plane"></i>
          Submit Form
        </button>
        <p style="margin-top: 1rem; color: #666; font-size: 0.9rem;">
          <i class="fas fa-info-circle"></i>
          Your form will be submitted and a PDF will be generated for your records.
        </p>
      </div>
    </form>
    <footer class="footer">
      © 2025 University of the Cordilleras. All Rights Reserved.
    </footer>

    <script>
      document.addEventListener("DOMContentLoaded", () => {
        /* For editing */
        const HOLIDAYS = [
          "2025-01-01",
          "2025-04-09",
          "2025-12-25",
        ]; /* sample holidates */
        const EARLIEST = "07:30";
        const LATEST = "19:30";
        const CURFEW_URL = "exemption-form.html";

        const btnPrint = document.getElementById("preparePrint");
        if (!btnPrint) {
          console.error("preparePrint button not found");
          return;
        }

        /* Time string to total minutes */
        const toMin = (t) => {
          const [h, m] = t.split(":").map(Number);
          return h * 60 + m;
        };

        /* Determine if curfew rules are triggered */
        function curfewReasons() {
          const date = document.getElementById("u_date")?.value;
          const start = document.getElementById("u_start")?.value;
          const end = document.getElementById("u_end")?.value;

          // Track reasoning
          const reasons = [];

          // uses from YYYY-MM-DD
          if (date) {
            const [year, month, day] = date.split("-").map(Number);
            const d = new Date(year, month - 1, day);

            const isSunday = d.getDay() === 0;
            if (isSunday) reasons.push("• Event is on a **Sunday**.");

            if (HOLIDAYS.includes(date)) {
              reasons.push("• Event is on a **holiday**.");
            }
          }

          // Exit early if no time inputs
          if (!start || !end) return reasons;

          const startMin = toMin(start);
          const endMin = toMin(end);
          const earliestMin = toMin(EARLIEST);
          const latestMin = toMin(LATEST);
          const duration = endMin - startMin;

          if (startMin < earliestMin) {
            reasons.push("• Starts **before 7:30 AM**.");
          }

          if (endMin > latestMin) {
            reasons.push("• Ends **after 7:30 PM**.");
          }



          return reasons;
        }

        /* Time conflict checking */
        async function checkTimeConflicts() {
          const date = document.getElementById("u_date")?.value;
          const start = document.getElementById("u_start")?.value;
          const end = document.getElementById("u_end")?.value;
          const venue = document.getElementById("u_venue")?.value || localStorage.getItem('selectedReservationVenue');

          if (!date || !start || !end || !venue) {
            hideTimeConflictWarning();
            return;
          }

          try {
            const response = await fetch('/api/reservations/check-time-conflict', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              },
              body: JSON.stringify({
                reservation_date: date,
                time_start: start + ':00',
                time_end: end + ':00',
                location: venue,
                venue: venue
              })
            });

            const result = await response.json();

            if (result.hasConflicts) {
              showTimeConflictWarning(result.conflicts, result.suggestedTimes);
            } else {
              hideTimeConflictWarning();
            }
          } catch (error) {
            console.error('Error checking time conflicts:', error);
            hideTimeConflictWarning();
          }
        }

        function showTimeConflictWarning(conflicts, suggestedTimes) {
          const warningDiv = document.getElementById('time-conflict-warning');
          const suggestionsDiv = document.getElementById('time-suggestions');

          if (warningDiv && suggestionsDiv) {
            warningDiv.style.display = 'block';

            // Clear previous suggestions
            suggestionsDiv.innerHTML = '';

            // Add suggested times
            if (suggestedTimes && suggestedTimes.length > 0) {
              suggestedTimes.forEach(timeSlot => {
                const suggestionBtn = document.createElement('div');
                suggestionBtn.className = 'time-suggestion';
                suggestionBtn.textContent = `${timeSlot.start} - ${timeSlot.end}`;
                suggestionBtn.onclick = () => {
                  document.getElementById('u_start').value = timeSlot.start_24h.slice(0, 5);
                  document.getElementById('u_end').value = timeSlot.end_24h.slice(0, 5);
                  hideTimeConflictWarning();
                };
                suggestionsDiv.appendChild(suggestionBtn);
              });
            } else {
              suggestionsDiv.innerHTML = '<p style="color: #666; font-style: italic;">No alternative times available for this date.</p>';
            }
          }
        }

        function hideTimeConflictWarning() {
          const warningDiv = document.getElementById('time-conflict-warning');
          if (warningDiv) {
            warningDiv.style.display = 'none';
          }
        }

        // Add event listeners for time conflict checking
        const timeStartInput = document.getElementById('u_start');
        const timeEndInput = document.getElementById('u_end');
        const dateInput = document.getElementById('u_date');
        const venueInput = document.getElementById('u_venue');

        if (timeStartInput) {
          timeStartInput.addEventListener('change', checkTimeConflicts);
        }
        if (timeEndInput) {
          timeEndInput.addEventListener('change', checkTimeConflicts);
        }
        if (dateInput) {
          dateInput.addEventListener('change', checkTimeConflicts);
        }
        if (venueInput) {
          venueInput.addEventListener('change', checkTimeConflicts);
        }

        /* Copy values from userform to internal form */
        function copyUserValues() {
          document
            .querySelectorAll(
              '#userForm [data-target]:not([type="checkbox"]):not([type="radio"])'
            )
            .forEach((src) => {
              const targetName = src.dataset.target;
              const srcValue = src.value;

              // Special handling for fields that map to checkboxes in combinedForm
              if (targetName === 'admission_fee') {
                // Clear all admission_fee checkboxes first
                document.querySelectorAll('#combinedForm input[name="admission_fee"]').forEach(cb => cb.checked = false);

                // Set appropriate checkbox based on text value
                if (srcValue.toLowerCase().includes('free')) {
                  const freeCheckbox = document.querySelector('#combinedForm input[name="admission_fee"][value="free"]');
                  if (freeCheckbox) freeCheckbox.checked = true;
                } else if (srcValue.toLowerCase().includes('php') || srcValue.match(/\d/)) {
                  const phpCheckbox = document.querySelector('#combinedForm input[name="admission_fee"][value="php"]');
                  if (phpCheckbox) phpCheckbox.checked = true;

                  // Extract amount if present
                  const amountMatch = srcValue.match(/\d+(\.\d+)?/);
                  if (amountMatch) {
                    const amountField = document.querySelector('#combinedForm input[name="admission_amount"]');
                    if (amountField) amountField.value = amountMatch[0];
                  }
                }
              } else if (targetName === 'audience_type') {
                // Clear all audience_type checkboxes first
                document.querySelectorAll('#combinedForm input[name="audience_type"]').forEach(cb => cb.checked = false);

                // Set appropriate checkbox based on text value
                if (srcValue.toLowerCase().includes('general')) {
                  const generalCheckbox = document.querySelector('#combinedForm input[name="audience_type"][value="general"]');
                  if (generalCheckbox) generalCheckbox.checked = true;
                } else if (srcValue.toLowerCase().includes('exclusive')) {
                  const exclusiveCheckbox = document.querySelector('#combinedForm input[name="audience_type"][value="exclusive"]');
                  if (exclusiveCheckbox) exclusiveCheckbox.checked = true;
                }
              } else {
                // Regular text field mapping
                const dst = document.getElementById(targetName);
                if (dst) {
                  dst.value = srcValue;
                  dst.textContent = srcValue;
                }
              }
            });

          // checkboxes or radios
          document
            .querySelectorAll(
              '#userForm input[type="checkbox"][data-target], #userForm input[type="radio"][data-target]'
            )
            .forEach((src) => {
              if (!src.checked) return;

              const tgtName = src.dataset.target;
              const tgtValue = src.dataset.value;

              let dst =
                tgtValue !== undefined
                  ? document.querySelector(
                      `#combinedForm input[name="${tgtName}"][value="${tgtValue}"]`
                    )
                  : document.getElementById(tgtName);

              if (dst) dst.checked = true;
            });
        }



        /* Make copyUserValues available globally for form submission */
        window.copyUserValues = copyUserValues;

        /* Populate exemption form with data from main form */
        function populateExemptionForm() {
          // Helper function to safely get element value
          const getElementValue = (id) => {
            const element = document.getElementById(id);
            return element ? element.value || '' : '';
          };

          // Helper function to safely set element value
          const setElementValue = (id, value) => {
            const element = document.getElementById(id);
            if (element) {
              element.value = value;
            }
          };

          // Populate exemption form fields safely
          setElementValue('exemption_office', getElementValue('u_org'));
          setElementValue('exemption_venue', getElementValue('u_venue'));
          setElementValue('exemption_people', getElementValue('u_attend'));
          setElementValue('exemption_date', getElementValue('u_date'));
          setElementValue('exemption_activity', getElementValue('u_event'));

          // Set times in 24-hour format for time inputs (they'll display as 12-hour automatically)
          const startTime = getElementValue('u_start');
          const endTime = getElementValue('u_end');

          setElementValue('exemption_start', startTime);
          setElementValue('exemption_end', endTime);
        }

        /* Format time from 24-hour to 12-hour format */
        function formatTimeTo12Hour(time24) {
          if (!time24) return '';
          const [hours, minutes] = time24.split(':');
          const hour24 = parseInt(hours, 10);
          const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
          const ampm = hour24 >= 12 ? 'PM' : 'AM';
          return `${hour12}:${minutes} ${ampm}`;
        }

        /* Function to proceed with submission after exemption form is shown */
        window.proceedWithSubmission = function() {
          // Validate exemption form fields
          const requiredFields = [
            { id: 'exemption_office', name: 'Requesting Office' },
            { id: 'exemption_venue', name: 'Venue' },
            { id: 'exemption_people', name: 'Number of People' },
            { id: 'exemption_date', name: 'Activity Date' },
            { id: 'exemption_activity', name: 'Title/Nature of Activity' },
            { id: 'exemption_start', name: 'Time Start' },
            { id: 'exemption_end', name: 'Time End' }
          ];

          const missingFields = [];
          requiredFields.forEach(field => {
            const element = document.getElementById(field.id);
            if (!element || !element.value.trim()) {
              missingFields.push(field.name);
            }
          });

          if (missingFields.length > 0) {
            showErrorModal({
              title: 'Exemption Form Incomplete',
              message: `Please fill out the following required fields in the exemption form:\n\n• ${missingFields.join('\n• ')}`,
              buttonText: 'OK'
            });
            return;
          }

          // Ensure the curfew flag is set so the check will pass
          const flag = document.getElementById("curfew_required");
          if (flag) flag.value = "Yes";

          // Call the form submission directly
          handleFormSubmission();
        };

        /* Update the curfew check to populate exemption form */
        const originalCheckCurfewRules = window.checkCurfewRules;
        window.checkCurfewRules = function() {
          const reasons = curfewReasons();

          if (reasons.length > 0) {
            // Check if exemption form is already visible and filled
            const exemptionSection = document.getElementById('exemption-section');
            const curfewFlag = document.getElementById("curfew_required");

            if (exemptionSection && exemptionSection.style.display === 'block' && curfewFlag && curfewFlag.value === "Yes") {
              // Exemption form is already shown and acknowledged, proceed with submission
              return true;
            }

            // First time curfew detected - populate and show exemption form
            populateExemptionForm();

            // Show modal for curfew exemption
            showConfirmationModal({
              title: 'Curfew Exemption Required',
              message: `This booking triggers curfew rules:\n\n${reasons.join('\n')}\n\nYou need to fill out the exemption form to proceed.`,
              confirmText: 'Fill Exemption Form',
              cancelText: 'Cancel',
              type: 'warning',
              onConfirm: () => {
                // Show exemption form section
                document.getElementById('exemption-section').style.display = 'block';
                document.getElementById('exemption-section').scrollIntoView({ behavior: 'smooth' });

                // Set flag that curfew form is needed
                const flag = document.getElementById("curfew_required");
                if (flag) flag.value = "Yes";

                return false; // Don't submit yet, user needs to fill exemption form
              },
              onCancel: () => {
                return false; // User cancelled
              }
            });
            return false; // Don't proceed with submission yet
          }

          return true; // No curfew issues, proceed normally
        };

        /* Update header info before printing */
        window.addEventListener("beforeprint", () => {
          const subDate = document.getElementById("submission-date");
          if (subDate) {
            subDate.textContent = new Date().toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            });
          }

          const ctrlIn = document.getElementById("control");
          const ctrlOut = document.getElementById("control-display");
          if (ctrlIn && ctrlOut) {
            ctrlOut.textContent = ctrlIn.value || "___________";
          }
        });
      });
    </script>
    <script>
  window.addEventListener("beforeprint", () => {
    const flag = document.getElementById("curfew_required");
    const exemptionDiv = document.getElementById("exemption-section");

    if (flag && flag.value === "Yes" && exemptionDiv) {
      exemptionDiv.style.display = "block";
    } else if (exemptionDiv) {
      exemptionDiv.style.display = "none";
    }
  });
</script>

    <!-- Include required JavaScript files -->
    <script src="/js/roleUtils.js"></script>
    <script src="/utils/navbar.js"></script>
    <script src="/utils/modals.js"></script>

    <script>
      // Initialize navbar and modals
      document.addEventListener('DOMContentLoaded', function() {
        // Check authentication and initialize navbar
        const token = localStorage.getItem('token');
        if (!token) {
          window.location.href = '/login';
          return;
        }

        // Verify user role and initialize navbar
        fetch('/api/auth/verify', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        })
        .then(response => response.json())
        .then(data => {
          if (!data.user) {
            window.location.href = '/login';
            return;
          }

          // Initialize navbar with correct user role
          initializeNavbar('user', 'internal-clients', data.user.role);

          // Initialize modals
          initializeModals();

          // Populate date field from calendar selection
          populateSelectedDate();
        })
        .catch(error => {
          console.error('Error verifying user:', error);
          window.location.href = '/login';
        });
      });

      // Function to populate the selected date and venue from calendar
      function populateSelectedDate() {
        const selectedDate = localStorage.getItem('selectedReservationDate');
        const selectedVenue = localStorage.getItem('selectedReservationVenue');
        const dateField = document.getElementById('u_date');
        const venueField = document.getElementById('u_venue');

        if (selectedDate && dateField) {
          dateField.value = selectedDate;
          console.log('Pre-populated date from calendar:', selectedDate);
        } else if (!selectedDate) {
          // If no date was selected from calendar, redirect back to reservation page
          alert('Please select a date from the reservation calendar first.');
          window.location.href = '/user/reservation.html';
        }

        if (selectedVenue && venueField) {
          venueField.value = selectedVenue;
          console.log('Pre-populated venue from calendar:', selectedVenue);

          // Trigger change event to update capacity if needed
          const changeEvent = new Event('change', { bubbles: true });
          venueField.dispatchEvent(changeEvent);
        }

        // Show existing reservations info if any
        const existingReservations = localStorage.getItem('existingReservations');
        if (existingReservations) {
          showExistingReservationsInfo(JSON.parse(existingReservations));
        }
      }

      // Function to show existing reservations for the selected date
      function showExistingReservationsInfo(reservations) {
        // Remove any existing info div
        const existingInfo = document.getElementById('existing-reservations-info');
        if (existingInfo) {
          existingInfo.remove();
        }

        const infoDiv = document.createElement('div');
        infoDiv.id = 'existing-reservations-info';
        infoDiv.className = 'alert alert-info';
        infoDiv.style.cssText = `
          margin: 15px 0;
          padding: 15px;
          background: #e3f2fd;
          border: 1px solid #2196f3;
          border-radius: 8px;
          color: #1565c0;
        `;

        infoDiv.innerHTML = `
          <div style="display: flex; align-items: center; margin-bottom: 10px;">
            <i class="fas fa-info-circle" style="margin-right: 8px; color: #2196f3;"></i>
            <strong>Existing Reservations for This Date:</strong>
          </div>
          <div style="margin-left: 20px;">
            ${reservations.map(res => `
              <div style="margin: 8px 0; padding: 8px; background: rgba(33, 150, 243, 0.1); border-radius: 4px;">
                <div style="font-weight: 500;">${res.title}</div>
                <div style="font-size: 0.9em; color: #666; margin-top: 4px;">
                  <i class="fas fa-clock" style="margin-right: 5px;"></i> ${res.time}
                  <span style="margin-left: 15px;">
                    <i class="fas fa-map-marker-alt" style="margin-right: 5px;"></i> ${res.venue || res.location}
                  </span>
                </div>
              </div>
            `).join('')}
          </div>
          <div style="margin-top: 10px; font-size: 0.9em; color: #1565c0;">
            <i class="fas fa-lightbulb" style="margin-right: 5px;"></i>
            <strong>Note:</strong> Choose different times to avoid conflicts.
          </div>
        `;

        // Insert after the venue field
        const venueField = document.getElementById('u_venue');
        if (venueField && venueField.parentNode) {
          venueField.parentNode.insertBefore(infoDiv, venueField.nextSibling);
        }
      }

      // Function to save reservation to calendar
      async function saveReservationToCalendar(formData) {
        try {
          const reservationData = {
            reservation_date: formData.u_date,
            time_start: formData.u_start,
            time_end: formData.u_end,
            form_type: 'internal',
            title: formData.u_event || 'Internal Client Event', // Function/Event field
            location: formData.u_venue || 'TBD',
            venue: formData.u_venue || localStorage.getItem('selectedReservationVenue') || 'TBD'
          };

          const response = await fetch('/api/reservations/save-date', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(reservationData)
          });

          if (!response.ok) {
            const error = await response.json();
            console.error('Failed to save reservation:', error);
            throw new Error(error.message || 'Failed to save reservation');
          }

          console.log('Reservation saved successfully');
        } catch (error) {
          console.error('Error saving reservation:', error);
          // Don't throw error here to allow form submission to continue
        }
      }

      // Handle form submission with PDF generation
      async function handleFormSubmission() {
        try {
          // Check curfew rules first
          if (!window.checkCurfewRules()) {
            return; // Stop submission if curfew rules are triggered and not acknowledged
          }

          // Get form data directly from the user form
          const formData = new FormData(document.getElementById('userForm'));

          // Convert FormData to regular object
          const formObject = {};
          for (let [key, value] of formData.entries()) {
            formObject[key] = value;
          }

          // Add checkbox values properly
          const checkboxes = document.querySelectorAll('#userForm input[type="checkbox"]:checked');
          checkboxes.forEach(checkbox => {
            if (checkbox.name && checkbox.value) {
              formObject[checkbox.name] = checkbox.value;
            }
          });

          // Add exemption form data if curfew is required
          if (formObject.curfew_required === "Yes") {
            const exemptionFields = [
              'exemption_office', 'exemption_venue', 'exemption_people',
              'exemption_date', 'exemption_activity', 'exemption_start', 'exemption_end'
            ];

            exemptionFields.forEach(fieldId => {
              const element = document.getElementById(fieldId);
              if (element && element.value) {
                formObject[fieldId] = element.value;
              }
            });


          }

          // Check authentication token
          const token = localStorage.getItem('token');

          if (!token) {
            showErrorModal({
              title: 'Authentication Required',
              message: 'Please log in to submit the form.',
              buttonText: 'Go to Login',
              onClose: () => {
                window.location.href = '/login';
              }
            });
            return;
          }



          // Show loading state
          const submitBtn = document.getElementById('preparePrint');
          submitBtn.disabled = true;
          submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Submitting...';

          // Submit form to backend
          const response = await fetch('/api/sarf/internal/submit', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(formObject)
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
          }

          const result = await response.json();

          // Reset button
          submitBtn.disabled = false;
          submitBtn.innerHTML = 'Submit';

          if (result.success) {
            // Save the reservation to calendar after successful form submission
            await saveReservationToCalendar(formObject);

            // Show success modal with PDF options
            showSuccessModalWithPDF({
              title: 'Form Submitted Successfully!',
              message: `Your internal client form has been submitted and PDF generated. Control Number: ${result.data.control}`,
              pdfUrl: result.data.pdfUrl,
              pdfId: result.data.id,
              controlNumber: result.data.control,
              onClose: () => {
                // Optionally redirect or reset form
                window.location.href = '/user/my-forms';
              }
            });
          } else {
            showErrorModal({
              title: 'Submission Failed',
              message: result.message || 'Failed to submit form. Please try again.',
              buttonText: 'Try Again'
            });
          }

        } catch (error) {

          // Reset button
          const submitBtn = document.getElementById('preparePrint');
          submitBtn.disabled = false;
          submitBtn.innerHTML = 'Submit';

          // More detailed error message
          let errorMessage = 'Unable to submit form. ';
          if (error.message.includes('Failed to fetch')) {
            errorMessage += 'Server connection failed. Please check if the server is running.';
          } else if (error.message.includes('401')) {
            errorMessage += 'Authentication failed. Please log in again.';
          } else if (error.message.includes('403')) {
            errorMessage += 'Access denied. Please check your permissions.';
          } else {
            errorMessage += `Error: ${error.message}`;
          }

          showErrorModal({
            title: 'Network Error',
            message: errorMessage,
            buttonText: 'Try Again'
          });
        }
      }
    </script>
  </body>
</html>
