<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>UC Calendar with Event Details</title>
  <link href="https://fonts.googleapis.com/css2?family=Poppins&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="stylesheet" href="/utils/navbar.css">
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }

    body {
      font-family: 'Poppins', sans-serif;
      background-color: #f0f4f1;
    }

    nav.navbar {
      position: sticky;
      top: 0;
      background-color: #2e7d32;
      color: #fff;
      padding: 15px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      z-index: 999;
    }

    .logo img {
      height: 75px;
    }

    .nav-links {
      display: flex;
      list-style: none;
      gap: 2rem;
    }

    .nav-links a {
      color: white;
      text-decoration: none;
      font-weight: 500;
      transition: 0.3s ease;
    }

    .nav-links a:hover {
      color: #a5d6a7;
    }

    .calendar-legend {
      text-align: center;
      margin: 20px auto;
      display: flex;
      justify-content: center;
      gap: 30px;
      flex-wrap: wrap;
    }

    .legend-circle {
      width: 15px;
      height: 15px;
      border-radius: 50%;
      display: inline-block;
      margin-right: 8px;
      vertical-align: middle;
    }

    .today { background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%); }
    .reserved { background: linear-gradient(135deg, #ff7043 0%, #ff5722 100%); }
    .pending { background: linear-gradient(135deg, #ffb74d 0%, #ff9800 100%); }
    .available { background-color: white; border: 1px solid #ccc; }

    .calendar-container {
      max-width: 1000px;
      margin: 0 auto;
      background: white;
      border-radius: 10px;
      padding: 20px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .calendar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }

    .calendar-header h2 {
      color: #2e7d32;
    }

    .calendar-controls {
      display: flex;
      gap: 0.5rem;
    }

    .nav-btn {
      background: #4caf50;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      cursor: pointer;
      transition: background-color 0.3s ease;
    }

    .nav-btn:hover {
      background: #45a049;
    }

    .venue-filter-container {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-bottom: 1rem;
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .venue-filter-container label {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 500;
      color: #333;
      white-space: nowrap;
    }

    .venue-filter-container label i {
      color: #4caf50;
    }

    .venue-filter-select {
      flex: 1;
      padding: 0.5rem;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 1rem;
      background: white;
      cursor: pointer;
    }

    .venue-filter-select:focus {
      outline: none;
      border-color: #4caf50;
    }

    .calendar-grid {
      display: grid;
      grid-template-columns: repeat(7, minmax(120px, 1fr));
      gap: 4px;
      padding: 1rem;
      max-width: 1000px;
      margin: 0 auto;
    }

    .day-name {
      background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
      color: white;
      text-align: center;
      padding: 0.8rem;
      font-weight: 600;
      font-size: 0.9rem;
      border-radius: 6px 6px 0 0;
      box-shadow: 0 2px 4px rgba(46, 125, 50, 0.2);
    }

    .calendar-day {
      background: white;
      text-align: center;
      padding: 0.4rem;
      cursor: pointer;
      transition: all 0.3s ease;
      min-height: 110px;
      max-height: 110px;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      font-weight: 500;
      flex-direction: column;
      position: relative;
      border-radius: 6px;
      overflow: hidden;
      font-size: 1.1rem;
      font-weight: 600;
      color: #333;
    }

    .calendar-day.today {
      background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
      color: white;
      box-shadow: 0 3px 8px rgba(46, 125, 50, 0.3);
      border: 2px solid #4caf50;
    }

    .calendar-day.reserved {
      background: linear-gradient(135deg, #ff7043 0%, #ff5722 100%);
      color: white;
      cursor: pointer;
      padding: 0.3rem;
      justify-content: space-between;
      align-items: stretch;
      box-shadow: 0 2px 4px rgba(255, 87, 34, 0.3);
      border: 1px solid #ff5722;
    }

    .calendar-day.pending {
      background: linear-gradient(135deg, #ffb74d 0%, #ff9800 100%);
      color: white;
      cursor: pointer;
      padding: 0.3rem;
      justify-content: space-between;
      align-items: stretch;
      box-shadow: 0 2px 4px rgba(255, 152, 0, 0.3);
      border: 1px solid #ff9800;
    }

    .calendar-day.empty {
      background: none;
      border: none;
      cursor: default;
    }

    .day-number {
      font-size: 0.9rem;
      font-weight: 700;
      position: absolute;
      top: 0.2rem;
      right: 0.3rem;
      background: rgba(255, 255, 255, 0.25);
      padding: 0.1rem 0.25rem;
      border-radius: 3px;
      min-width: 1.2rem;
      text-align: center;
      z-index: 2;
    }

    .event-title {
      font-size: 0.65rem;
      font-weight: 600;
      line-height: 1.1;
      margin-top: 1.5rem;
      margin-bottom: 0.1rem;
      text-align: left;
      word-wrap: break-word;
      flex: 1;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      max-height: 2.2rem;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .event-location {
      font-size: 0.55rem;
      opacity: 0.95;
      line-height: 1;
      text-align: left;
      word-wrap: break-word;
      background: rgba(255, 255, 255, 0.2);
      padding: 0.15rem 0.25rem;
      border-radius: 2px;
      font-weight: 500;
      max-height: 1.1rem;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    /* Modal */
    .modal {
      display: none;
      position: fixed;
      top: 0; left: 0;
      width: 100vw; height: 100vh;
      background-color: rgba(0, 0, 0, 0.5);
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }

    .modal-content {
      background-color: white;
      padding: 30px;
      border-radius: 10px;
      width: 90%;
      max-width: 400px;
      text-align: left;
      box-shadow: 0 4px 16px rgba(0,0,0,0.2);
      position: relative;
    }

    .modal-content h3 {
      margin-bottom: 15px;
      color: #2e7d32;
    }

    .modal-content p {
      margin: 5px 0;
      color: #333;
    }

    .close-btn {
      position: absolute;
      top: 15px;
      right: 20px;
      font-size: 28px;
      color: #666;
      cursor: pointer;
      background: none;
      border: none;
      padding: 5px;
      line-height: 1;
      transition: color 0.3s ease;
    }

    .close-btn:hover {
      color: #333;
    }

    .additional-info {
      margin-top: 15px;
      padding-top: 15px;
      border-top: 1px solid #e0e0e0;
    }

    .additional-info p {
      margin: 8px 0;
      color: #333;
    }

    .status-pending {
      color: #ff9800;
      font-weight: bold;
      text-transform: capitalize;
      background: rgba(255, 152, 0, 0.1);
      padding: 2px 6px;
      border-radius: 4px;
    }

    .status-confirmed, .status-approved {
      color: #4caf50;
      font-weight: bold;
      text-transform: capitalize;
      background: rgba(76, 175, 80, 0.1);
      padding: 2px 6px;
      border-radius: 4px;
    }

    .status-cancelled, .status-rejected {
      color: #f44336;
      font-weight: bold;
      text-transform: capitalize;
      background: rgba(244, 67, 54, 0.1);
      padding: 2px 6px;
      border-radius: 4px;
    }

    @media (max-width: 768px) {
      .calendar-grid {
        grid-template-columns: repeat(7, minmax(90px, 1fr));
        gap: 2px;
        padding: 0.5rem;
      }

      .calendar-day {
        min-height: 90px;
        max-height: 90px;
        padding: 0.25rem;
      }

      .day-number {
        font-size: 0.75rem;
        top: 0.15rem;
        right: 0.25rem;
        padding: 0.05rem 0.2rem;
      }

      .event-title {
        font-size: 0.55rem;
        margin-top: 1.2rem;
        max-height: 1.8rem;
      }

      .event-location {
        font-size: 0.5rem;
        padding: 0.1rem 0.2rem;
      }

      .calendar-container {
        margin: 1rem;
        padding: 15px;
      }

      .calendar-legend {
        gap: 15px;
        font-size: 0.9rem;
      }
    }

  </style>
</head>
<body>

  <!-- Navbar Container -->
  <div id="navbar-container"></div>

  <!-- Legend -->
  <div class="calendar-legend">
    <div><span class="legend-circle today"></span> Today</div>
    <div><span class="legend-circle reserved"></span> Approved Reservations</div>
    <div><span class="legend-circle pending" style="background: linear-gradient(135deg, #ffb74d 0%, #ff9800 100%);"></span> Pending Requests</div>
    <div><span class="legend-circle available"></span> Available Dates</div>
  </div>

  <!-- Calendar -->
  <div class="calendar-container">
    <div class="calendar-header">
      <h2 id="monthYear"></h2>
      <div class="calendar-controls">
        <button id="prevMonth" class="nav-btn">
          <i class="fas fa-chevron-left"></i>
        </button>
        <button id="nextMonth" class="nav-btn">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>

    <!-- Venue Filter -->
    <div class="venue-filter-container">
      <label for="admin-venue-filter">
        <i class="fas fa-filter"></i>
        Filter by Venue:
      </label>
      <select id="admin-venue-filter" class="venue-filter-select">
        <option value="">All Venues</option>
        <!-- Main Campus -->
        <option value="Auditorium">Auditorium</option>
        <option value="Cañao Hall">Cañao Hall</option>
        <option value="Conference Room">Conference Room</option>
        <option value="Gymnasium">Gymnasium</option>
        <option value="Laboratory Room">Laboratory Room</option>
        <option value="Lecture Room (Main)">Lecture Room (Main)</option>
        <option value="Training Center">Training Center</option>
        <!-- Legarda -->
        <option value="AVR">AVR</option>
        <option value="Crime Lab">Crime Lab</option>
        <option value="Deftac">Deftac</option>
        <option value="Lecture Room (Legarda)">Lecture Room (Legarda)</option>
        <option value="Moot Court">Moot Court</option>
      </select>
    </div>
    <div class="calendar-grid" id="calendar">
      <div class="day-name">Sun</div>
      <div class="day-name">Mon</div>
      <div class="day-name">Tue</div>
      <div class="day-name">Wed</div>
      <div class="day-name">Thu</div>
      <div class="day-name">Fri</div>
      <div class="day-name">Sat</div>
    </div>
  </div>

  <!-- Modal -->
  <div id="eventModal" class="modal" onclick="closeModal(event)">
    <div class="modal-content" onclick="event.stopPropagation()">
      <button class="close-btn" onclick="closeModal()">&times;</button>
      <h3 id="modalTitle">Event Title</h3>
      <p><strong>Time:</strong> <span id="modalTime"></span></p>
      <p><strong>Location:</strong> <span id="modalLocation"></span></p>
      <div class="additional-info" id="additionalInfo"></div>
    </div>
  </div>

  <!-- JavaScript -->
  <script>
    let reservedEvents = {};
    let calendarData = {};
    let allCalendarData = {};
    let currentVenueFilter = '';
    let currentDate = new Date();

    // Load calendar data from API
    async function loadCalendarData() {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch('/api/reservations/admin/calendar', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          allCalendarData = data.calendarData || {};
          calendarData = { ...allCalendarData };

          // Convert to the format expected by existing code
          updateReservedEvents();
          renderCalendar();
        }
      } catch (error) {
        console.error('Error loading calendar data:', error);
        // Fallback to mock data
        loadMockData();
      }
    }

    // Fallback mock data
    function loadMockData() {
      reservedEvents = {
        "2025-06-15": { title: "Faculty Meeting", time: "10:00 AM – 12:00 PM", location: "AVR Room", status: "confirmed", user: "Admin", type: "event" },
        "2025-06-20": { title: "Student Orientation", time: "1:00 PM – 4:00 PM", location: "Auditorium", status: "confirmed", user: "Admin", type: "event" },
        "2025-06-23": { title: "Defense Presentation", time: "9:00 AM – 11:00 AM", location: "Lecture Room 101", status: "pending", user: "John Doe", type: "internal" }
      };
      renderCalendar();
    }

    function renderCalendar() {
      const calendar = document.getElementById("calendar");
      const monthYear = document.getElementById("monthYear");

      const year = currentDate.getFullYear();
      const month = currentDate.getMonth();
      const todayStr = now.toISOString().split("T")[0];

      const firstDay = new Date(year, month, 1).getDay();
      const lastDate = new Date(year, month + 1, 0).getDate();

      const monthNames = ["January","February","March","April","May","June","July","August","September","October","November","December"];
      monthYear.textContent = `${monthNames[month]} ${year}`;

      // Clear existing calendar days (keep headers)
      const dayHeaders = calendar.querySelectorAll('.day-name');
      calendar.innerHTML = '';
      dayHeaders.forEach(header => calendar.appendChild(header));

      // Add empty cells for days before the first day of the month
      for (let i = 0; i < firstDay; i++) {
        const empty = document.createElement("div");
        empty.classList.add("calendar-day", "empty");
        calendar.appendChild(empty);
      }

      // Add days of the month
      for (let d = 1; d <= lastDate; d++) {
        const dateStr = `${year}-${String(month + 1).padStart(2, "0")}-${String(d).padStart(2, "0")}`;
        const cell = document.createElement("div");
        cell.classList.add("calendar-day");
        cell.textContent = d;

        if (dateStr === todayStr) {
          cell.classList.add("today");
        }

        if (reservedEvents[dateStr]) {
          const event = reservedEvents[dateStr];

          // Add appropriate class based on status
          if (event.status === 'pending') {
            cell.classList.add("pending");
          } else {
            cell.classList.add("reserved");
          }

          // Create content structure for reserved/pending dates
          const dayNumber = document.createElement('div');
          dayNumber.className = 'day-number';
          dayNumber.textContent = d;

          const eventTitle = document.createElement('div');
          eventTitle.className = 'event-title';
          eventTitle.textContent = truncateText(event.title, 20);
          eventTitle.title = event.title;

          const eventLocation = document.createElement('div');
          eventLocation.className = 'event-location';
          eventLocation.textContent = truncateText(event.location || 'TBD', 25);
          eventLocation.title = event.location || 'TBD';

          // Clear the day number and add structured content
          cell.textContent = '';
          cell.appendChild(dayNumber);
          cell.appendChild(eventTitle);
          cell.appendChild(eventLocation);

          cell.onclick = () => showModal(dateStr);
          cell.style.cursor = 'pointer';
        }

        calendar.appendChild(cell);
      }
    }

    // Function to truncate text with ellipsis
    function truncateText(text, maxLength) {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength - 3) + '...';
    }

    function showModal(date) {
      const event = reservedEvents[date];
      document.getElementById("modalTitle").textContent = event.title;
      document.getElementById("modalTime").textContent = event.time || 'Not specified';
      document.getElementById("modalLocation").textContent = event.location || 'TBD';

      // Add additional information for reservations
      const additionalInfo = document.getElementById('additionalInfo');

      if (event.user && event.type) {
        additionalInfo.innerHTML = `
          <p><strong>Requested by:</strong> ${event.user}</p>
          <p><strong>Type:</strong> ${event.type.charAt(0).toUpperCase() + event.type.slice(1)}</p>
          <p><strong>Status:</strong> <span class="status-${event.status}">${event.status.charAt(0).toUpperCase() + event.status.slice(1)}</span></p>
        `;
        additionalInfo.style.display = 'block';
      } else {
        additionalInfo.style.display = 'none';
      }

      document.getElementById("eventModal").style.display = "flex";
    }

    function closeModal(event) {
      // If event is passed and it's not the modal background, don't close
      if (event && event.target !== event.currentTarget) {
        return;
      }
      document.getElementById("eventModal").style.display = "none";
    }

    // Close modal with Escape key
    document.addEventListener('keydown', function(event) {
      if (event.key === 'Escape') {
        closeModal();
      }
    });

    function updateReservedEvents() {
      reservedEvents = {};
      Object.keys(calendarData).forEach(date => {
        if (calendarData[date].length > 0) {
          const firstEvent = calendarData[date][0];
          reservedEvents[date] = {
            title: firstEvent.title,
            time: firstEvent.time,
            location: firstEvent.location,
            venue: firstEvent.venue,
            status: firstEvent.status,
            user: firstEvent.user,
            type: firstEvent.type
          };
        }
      });
    }

    function applyVenueFilter() {
      if (currentVenueFilter === '') {
        calendarData = { ...allCalendarData };
      } else {
        calendarData = {};
        Object.keys(allCalendarData).forEach(date => {
          const filteredEvents = allCalendarData[date].filter(event =>
            event.venue === currentVenueFilter || event.location === currentVenueFilter
          );
          if (filteredEvents.length > 0) {
            calendarData[date] = filteredEvents;
          }
        });
      }
      updateReservedEvents();
      renderCalendar();
    }

    function initializeControls() {
      // Month navigation
      document.getElementById('prevMonth').addEventListener('click', function() {
        currentDate.setMonth(currentDate.getMonth() - 1);
        renderCalendar();
      });

      document.getElementById('nextMonth').addEventListener('click', function() {
        currentDate.setMonth(currentDate.getMonth() + 1);
        renderCalendar();
      });

      // Venue filter
      document.getElementById('admin-venue-filter').addEventListener('change', function() {
        currentVenueFilter = this.value;
        applyVenueFilter();
      });
    }

    // Initialize calendar on page load
    document.addEventListener('DOMContentLoaded', function() {
      loadCalendarData();
      initializeControls();
    });

    // Initial render for fallback
    renderCalendar();
  </script>

  <script>
    // Initialize navbar and check authentication
    document.addEventListener('DOMContentLoaded', function() {
      // Initialize admin navbar - will be updated with user role after auth
      initializeNavbar('admin', 'calendar');

      // Check authentication
      const token = localStorage.getItem('token');
      if (!token) {
        window.location.href = '/login';
        return;
      }

      // Verify admin role
      fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(response => response.json())
      .then(data => {
        if (!data.user || !isAdminRole(data.user.role)) {
          if (data.user && (data.user.role === 'student' || data.user.role === 'external')) {
            window.location.href = '/home';
          } else {
            window.location.href = '/login';
          }
        } else {
          // Re-initialize navbar with correct role
          initializeNavbar('admin', 'calendar', data.user.role);
        }
      })
      .catch(error => {
        window.location.href = '/login';
      });
    });
  </script>
  <script src="/js/roleUtils.js"></script>
  <script src="/utils/navbar.js"></script>
</body>
</html>
