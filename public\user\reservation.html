<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Reservation - Rules & Calendar</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
  <link rel="stylesheet" href="/utils/navbar.css">
  <link rel="stylesheet" href="/utils/modals.css">
  <link rel="stylesheet" href="/user/styles/reservation-calendar.css">
</head>
<body>
  <div id="navbar-container"></div>

  <!-- Rules and Regulations Section -->
  <div id="rules-section" class="rules-container">
    <div class="rules-content">
      <div class="rules-header">
        <i class="fas fa-gavel"></i>
        <h1>Rules and Regulations</h1>
        <p>Please read and accept the following terms before proceeding with your reservation</p>
      </div>

      <div class="rules-body">
        <div class="rule-item">
          <h3><i class="fas fa-clock"></i> Booking Requirements</h3>
          <ul>
            <li>All reservations must be submitted at least <strong>2 weeks in advance</strong></li>
            <li>Submit <strong>two (2) copies</strong> of the completed form</li>
            <li>Include a signed request letter from your department/organization</li>
          </ul>
        </div>

        <div class="rule-item">
          <h3><i class="fas fa-calendar-check"></i> Reservation Policies</h3>
          <ul>
            <li>Reservations are subject to availability and approval</li>
            <li>Maximum booking duration is <strong>8 hours per day</strong></li>
            <li>No same-day reservations will be accepted</li>
            <li>Cancellations must be made at least 48 hours in advance</li>
          </ul>
        </div>

        <div class="rule-item">
          <h3><i class="fas fa-users"></i> Facility Usage</h3>
          <ul>
            <li>Respect the maximum capacity of each venue</li>
            <li>Keep facilities clean and organized</li>
            <li>Report any damages immediately to the FMO office</li>
            <li>No smoking, eating, or drinking in certain designated areas</li>
          </ul>
        </div>

        <div class="rule-item">
          <h3><i class="fas fa-exclamation-triangle"></i> Violations & Penalties</h3>
          <ul>
            <li>Failure to comply may result in <strong>reservation cancellation</strong></li>
            <li>Repeated violations may lead to <strong>booking privileges suspension</strong></li>
            <li>Damage to facilities will incur <strong>repair/replacement costs</strong></li>
          </ul>
        </div>
      </div>

      <div class="rules-footer">
        <div class="acceptance-checkbox">
          <input type="checkbox" id="accept-rules" required>
          <label for="accept-rules">
            I have read and agree to abide by all the rules and regulations stated above
          </label>
        </div>

        <div class="rules-actions">
          <button type="button" class="btn-decline" onclick="declineRules()">
            <i class="fas fa-times"></i> Decline
          </button>
          <button type="button" class="btn-accept" id="accept-btn" onclick="acceptRules()" disabled>
            <i class="fas fa-check"></i> Accept & Continue
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Venue Selection Section (Initially Hidden) -->
  <div id="venue-section" class="venue-container" style="display: none;">
    <div class="venue-content">
      <div class="venue-header">
        <i class="fas fa-building"></i>
        <h1>Select Venue</h1>
        <p>Choose the venue for your reservation from Main Campus or Legarda Campus</p>
      </div>

      <div class="venue-grid">
        <!-- Main Campus Venues -->
        <div class="campus-section">
          <h3 class="campus-title"><i class="fas fa-building"></i> Main Campus</h3>
          <div class="campus-venues">
            <div class="venue-card" data-venue="Auditorium">
              <div class="venue-icon">
                <i class="fas fa-theater-masks"></i>
              </div>
              <h4>Auditorium</h4>
              <p>Large capacity venue for events</p>
            </div>

            <div class="venue-card" data-venue="Cañao Hall">
              <div class="venue-icon">
                <i class="fas fa-users"></i>
              </div>
              <h4>Cañao Hall</h4>
              <p>Multi-purpose hall</p>
            </div>

            <div class="venue-card" data-venue="Conference Room">
              <div class="venue-icon">
                <i class="fas fa-handshake"></i>
              </div>
              <h4>Conference Room</h4>
              <p>Professional meeting space</p>
            </div>

            <div class="venue-card" data-venue="Gymnasium">
              <div class="venue-icon">
                <i class="fas fa-dumbbell"></i>
              </div>
              <h4>Gymnasium</h4>
              <p>Sports and physical activities</p>
            </div>

            <div class="venue-card" data-venue="Laboratory Room">
              <div class="venue-icon">
                <i class="fas fa-flask"></i>
              </div>
              <h4>Laboratory Room</h4>
              <p>Science and research lab</p>
            </div>

            <div class="venue-card" data-venue="Lecture Room (Main)">
              <div class="venue-icon">
                <i class="fas fa-chalkboard"></i>
              </div>
              <h4>Lecture Room (Main)</h4>
              <p>Academic presentations</p>
            </div>

            <div class="venue-card" data-venue="Training Center">
              <div class="venue-icon">
                <i class="fas fa-graduation-cap"></i>
              </div>
              <h4>Training Center</h4>
              <p>Skills development and training</p>
            </div>
          </div>
        </div>

        <!-- Legarda Campus Venues -->
        <div class="campus-section">
          <h3 class="campus-title"><i class="fas fa-university"></i> Legarda Campus</h3>
          <div class="campus-venues">
            <div class="venue-card" data-venue="AVR">
              <div class="venue-icon">
                <i class="fas fa-video"></i>
              </div>
              <h4>AVR</h4>
              <p>Audio-Visual Room</p>
            </div>

            <div class="venue-card" data-venue="Crime Lab">
              <div class="venue-icon">
                <i class="fas fa-search"></i>
              </div>
              <h4>Crime Lab</h4>
              <p>Forensic laboratory</p>
            </div>

            <div class="venue-card" data-venue="Deftac">
              <div class="venue-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <h4>Deftac</h4>
              <p>Defense tactics training</p>
            </div>

            <div class="venue-card" data-venue="Lecture Room (Legarda)">
              <div class="venue-icon">
                <i class="fas fa-chalkboard-teacher"></i>
              </div>
              <h4>Lecture Room (Legarda)</h4>
              <p>Academic presentations</p>
            </div>

            <div class="venue-card" data-venue="Moot Court">
              <div class="venue-icon">
                <i class="fas fa-gavel"></i>
              </div>
              <h4>Moot Court</h4>
              <p>Legal practice and simulation</p>
            </div>
          </div>
        </div>
      </div>

      <div class="venue-footer">
        <div class="selected-venue-info" id="selected-venue-info" style="display: none;">
          <i class="fas fa-check-circle"></i>
          <span>Selected Venue: <strong id="selected-venue-display"></strong></span>
        </div>

        <div class="venue-actions">
          <button type="button" class="btn-back" onclick="goBackToRules()">
            <i class="fas fa-arrow-left"></i> Back to Rules
          </button>
          <button type="button" class="btn-continue" id="venue-continue-btn" onclick="proceedToCalendar()" disabled>
            <i class="fas fa-arrow-right"></i> Continue to Calendar
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Calendar Section (Initially Hidden) -->
  <div id="calendar-section" class="calendar-container" style="display: none;">
    <div class="calendar-content">
      <div class="calendar-header">
        <h2><i class="fas fa-calendar-alt"></i> Select Your Preferred Date</h2>
        <p>Choose an available date for your reservation at <strong id="calendar-venue-display"></strong></p>
        <div class="calendar-navigation">
          <button type="button" id="prev-month" class="nav-btn">
            <i class="fas fa-chevron-left"></i>
          </button>
          <span id="current-month-year"></span>
          <button type="button" id="next-month" class="nav-btn">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>
      </div>

      <!-- Venue Filter -->
      <div class="venue-filter-container">
        <label for="venue-filter">
          <i class="fas fa-filter"></i>
          Filter by Venue:
        </label>
        <select id="venue-filter" class="venue-filter-select" disabled>
          <option value="">Select venue first</option>
          <!-- Main Campus -->
          <option value="Auditorium">Auditorium</option>
          <option value="Cañao Hall">Cañao Hall</option>
          <option value="Conference Room">Conference Room</option>
          <option value="Gymnasium">Gymnasium</option>
          <option value="Laboratory Room">Laboratory Room</option>
          <option value="Lecture Room (Main)">Lecture Room (Main)</option>
          <option value="Training Center">Training Center</option>
          <!-- Legarda -->
          <option value="AVR">AVR</option>
          <option value="Crime Lab">Crime Lab</option>
          <option value="Deftac">Deftac</option>
          <option value="Lecture Room (Legarda)">Lecture Room (Legarda)</option>
          <option value="Moot Court">Moot Court</option>
        </select>
      </div>

      <!-- Calendar Legend -->
      <div class="calendar-legend">
        <div class="legend-item">
          <span class="legend-circle today"></span>
          <span>Today</span>
        </div>
        <div class="legend-item">
          <span class="legend-circle available"></span>
          <span>Available</span>
        </div>
        <div class="legend-item">
          <span class="legend-circle reserved"></span>
          <span>Reserved</span>
        </div>
        <div class="legend-item">
          <span class="legend-circle selected"></span>
          <span>Selected</span>
        </div>
      </div>

      <div class="calendar-grid">
        <div class="day-header">Sun</div>
        <div class="day-header">Mon</div>
        <div class="day-header">Tue</div>
        <div class="day-header">Wed</div>
        <div class="day-header">Thu</div>
        <div class="day-header">Fri</div>
        <div class="day-header">Sat</div>
      </div>

      <div class="calendar-footer">
        <div class="selected-date-info" id="selected-date-info" style="display: none;">
          <i class="fas fa-calendar-check"></i>
          <span>Selected Date: <strong id="selected-date-display"></strong></span>
        </div>

        <div class="calendar-actions">
          <button type="button" class="btn-back" onclick="goBackToVenue()">
            <i class="fas fa-arrow-left"></i> Back to Venue
          </button>
          <button type="button" class="btn-continue" id="continue-btn" onclick="proceedToForms()" disabled>
            <i class="fas fa-arrow-right"></i> Continue to Forms
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Form Selection Section (Initially Hidden) -->
  <div id="form-selection" class="form-selection-container" style="display: none;">
    <div class="form-selection-content">
      <h2><i class="fas fa-clipboard-list"></i> Select Reservation Type</h2>
      <p>Choose the appropriate form for your reservation</p>

      <div class="form-options">
        <a href="#" class="form-option-card" onclick="navigateToForm('on-campus.html')">
          <i class="fas fa-university"></i>
          <h3>On Campus</h3>
          <p>For campus-based activities and events</p>
        </a>
        <a href="#" class="form-option-card" onclick="navigateToForm('internal-clients.html')">
          <i class="fas fa-users"></i>
          <h3>Internal Clients</h3>
          <p>For university departments and organizations</p>
        </a>
        <a href="#" class="form-option-card" onclick="navigateToForm('external-clients.html')">
          <i class="fas fa-handshake"></i>
          <h3>External Clients</h3>
          <p>For external organizations and partners</p>
        </a>
      </div>
    </div>
  </div>

  <script src="/utils/modals.js"></script>
  <script src="/user/scripts/reservation-calendar.js"></script>
  <script src="/utils/navbar.js"></script>

  <script>
    // Initialize navbar
    document.addEventListener('DOMContentLoaded', function() {
      initializeNavbar('user', 'reservation', 'student');
    });
  </script>


</body>
</html>
