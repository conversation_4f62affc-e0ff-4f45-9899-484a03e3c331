<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UC Campus - Events Management</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="/utils/navbar.css">
    <link rel="stylesheet" href="/utils/modals.css">
    <style>
        .main-content {
            display: none;
        }
        
        .main-content.show {
            display: block;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: #e8f5e9;
            min-height: 100vh;
            flex-direction: column;
            margin: 0 !important;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .page-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .page-header h1 {
            color: #1b5e20;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .page-header p {
            color: #4e944f;
            font-size: 1.1rem;
        }

        .form-container {
            background: white;
            border-radius: 12px;
            padding: 50px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 40px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #1b5e20;
            font-weight: 600;
        }

        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #c5e1a5;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #2e7d32;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }

        .submit-btn {
            background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            width: 100%;
        }

        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(46, 125, 50, 0.3);
        }

        .submit-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .events-list {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .events-list h2 {
            color: #1b5e20;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .event-item {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            transition: box-shadow 0.3s ease;
        }

        .event-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .event-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .event-title {
            color: #1b5e20;
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        .delete-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .delete-btn:hover {
            background: #c82333;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        .event-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            color: #666;
        }

        .event-detail {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .event-detail i {
            color: #2e7d32;
            width: 16px;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .no-events {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin-top: 30px;
        }

        .page-info {
            color: #1b5e20;
            font-weight: 600;
            font-size: 1rem;
            min-width: 120px;
            text-align: center;
        }

        .pagination button {
            display: flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .pagination button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(46, 125, 50, 0.3);
        }

        .pagination button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 20px 10px;
            }

            .pagination {
                flex-direction: column;
                gap: 15px;
            }

            .pagination button {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div id="auth-error" style="display: none; text-align: center; padding: 20px; background: #f8d7da; color: #721c24;">
        <h2>Authentication Required</h2>
        <p>You need to be logged in as an admin to access this page.</p>
        <a href="/login" style="color: #721c24; text-decoration: underline;">Go to Login</a>
    </div>

    <div class="main-content">
        <div id="navbar-container"></div>
        
        <div class="container">
            <div class="page-header">
                <h1>Events Management</h1>
                <p>Create and manage upcoming events for the UC Campus</p>
            </div>

            <div id="alert" class="alert"></div>

            <div class="form-container">
                <h2 style="color: #1b5e20; margin-bottom: 20px;">Add New Event</h2>
                <form id="eventForm">
                    <div class="form-group">
                        <label for="title">Event Title *</label>
                        <input type="text" id="title" name="title" required maxlength="100" placeholder="Enter event title">
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="date">Date *</label>
                            <input type="date" id="date" name="date" required>
                        </div>
                        <div class="form-group">
                            <label for="time">Time *</label>
                            <input type="text" id="time" name="time" placeholder="e.g., 8:00 AM - 12:00 PM" required maxlength="50">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="location">Location *</label>
                        <input type="text" id="location" name="location" placeholder="e.g., Function Hall, Main Campus" required maxlength="100">
                    </div>

                    <div class="form-group">
                        <label for="event_image">Event Image (Optional)</label>
                        <input type="file" id="event_image" name="event_image" accept="image/*">
                        <small style="color: #666; font-size: 0.9rem;">Upload an image for this event (JPG, PNG, GIF)</small>
                    </div>

                    <button type="submit" class="submit-btn" id="submitBtn">
                        <i class="fas fa-plus"></i> Create Event
                    </button>
                </form>
            </div>

            <div class="events-list">
                <h2>Upcoming Events</h2>
                <div id="eventsList">
                    <div class="no-events">Loading events...</div>
                </div>
                <div class="pagination" id="eventsPagination" style="display: none;">
                    <button id="prevEventsBtn"><i class="fas fa-chevron-left"></i> Previous</button>
                    <span id="eventsPageInfo" class="page-info">Page 1 of 1</span>
                    <button id="nextEventsBtn">Next <i class="fas fa-chevron-right"></i></button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let allEvents = [];
        let currentEventsPage = 1;
        const eventsPerPage = 3;

        function getToken() {
            return localStorage.getItem('token');
        }

        function showAlert(message, type) {
            const alert = document.getElementById('alert');
            alert.textContent = message;
            alert.className = `alert ${type}`;
            alert.style.display = 'block';
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        async function loadEvents() {
            try {
                const response = await fetch('/api/events');
                if (response.ok) {
                    allEvents = await response.json();
                    currentEventsPage = 1;
                    displayEvents();
                } else {
                    const errorData = await response.json();
                    console.error('Failed to load events:', errorData);
                    throw new Error(errorData.message || 'Failed to load events');
                }
            } catch (error) {
                console.error('Error loading events:', error);
                allEvents = [];
                document.getElementById('eventsList').innerHTML = `<div class="no-events">
                    <img src="/images/NoEventsShowcase.png" alt="No Events" style="width: 150px; max-width: 400px; display: block; margin: 0 auto;" />
                    <p>Error loading events: ${error.message}</p>
                </div>`;
                document.getElementById('eventsPagination').style.display = 'none';
            }
        }

        function displayEvents() {
            const container = document.getElementById('eventsList');
            const pagination = document.getElementById('eventsPagination');

            if (allEvents.length === 0) {
                container.innerHTML = `<div class="no-events">
                    <img src="/images/NoEventsShowcase.png" alt="No Events" style="width: 150px; max-width: 400px; display: block; margin: 0 auto 20px auto;" />
                    <p>No upcoming events</p>
                </div>`;
                pagination.style.display = 'none';
                return;
            }

            // Calculate pagination
            const startIndex = (currentEventsPage - 1) * eventsPerPage;
            const endIndex = startIndex + eventsPerPage;
            const currentEvents = allEvents.slice(startIndex, endIndex);

            container.innerHTML = currentEvents.map(event => `
                <div class="event-item">
                    <div class="event-header">
                        <div class="event-title">${event.title}</div>
                        <button class="delete-btn" onclick="deleteEvent(${event.id})" title="Delete Event">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="event-details">
                        <div class="event-detail">
                            <i class="fas fa-calendar"></i>
                            <span>${formatDate(event.date)}</span>
                        </div>
                        <div class="event-detail">
                            <i class="fas fa-clock"></i>
                            <span>${event.time}</span>
                        </div>
                        <div class="event-detail">
                            <i class="fas fa-map-marker-alt"></i>
                            <span>${event.location}</span>
                        </div>
                    </div>
                </div>
            `).join('');

            updateEventsPagination();
        }

        function updateEventsPagination() {
            const totalPages = Math.ceil(allEvents.length / eventsPerPage);
            const prevBtn = document.getElementById('prevEventsBtn');
            const nextBtn = document.getElementById('nextEventsBtn');
            const pageInfo = document.getElementById('eventsPageInfo');
            const pagination = document.getElementById('eventsPagination');

            if (totalPages > 1) {
                pagination.style.display = 'flex';
                prevBtn.disabled = currentEventsPage === 1;
                nextBtn.disabled = currentEventsPage === totalPages;
                pageInfo.textContent = `Page ${currentEventsPage} of ${totalPages}`;
            } else {
                pagination.style.display = 'none';
            }
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: '2-digit'
            });
        }

        async function deleteEvent(eventId) {
            const confirmed = await showConfirmationModal({
                title: 'Delete Event',
                message: 'Are you sure you want to delete this event? This action cannot be undone.',
                confirmText: 'Delete',
                cancelText: 'Cancel',
                type: 'danger'
            });

            if (!confirmed) {
                return;
            }

            try {
                const token = getToken();
                const response = await fetch(`/api/events/${eventId}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (response.ok) {
                    await showSuccessModal({
                        title: 'Event Deleted!',
                        message: 'The event has been successfully deleted.',
                        autoClose: 3000
                    });

                    // Check if current page becomes empty after deletion
                    const totalPages = Math.ceil((allEvents.length - 1) / eventsPerPage);
                    if (currentEventsPage > totalPages && totalPages > 0) {
                        currentEventsPage = totalPages;
                    }

                    loadEvents();
                } else {
                    showErrorModal({
                        title: 'Delete Failed',
                        message: result.message || 'Could not delete the event. Please try again.'
                    });
                }
            } catch (error) {
                showErrorModal({
                    title: 'Network Error',
                    message: 'Could not connect to the server. Please check your connection and try again.'
                });
            }
        }

        document.getElementById('eventForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';

            const formData = new FormData(this);

            try {
                const token = getToken();

                const response = await fetch('/api/events', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                const result = await response.json();

                if (response.ok) {
                    await showSuccessModal({
                        title: 'Event Created!',
                        message: 'Your event has been successfully created and will appear on the home page.',
                        autoClose: 4000
                    });
                    this.reset();
                    loadEvents();
                } else {
                    showErrorModal({
                        title: 'Creation Failed',
                        message: result.message || 'Could not create the event. Please check your input and try again.'
                    });
                }
            } catch (error) {
                showErrorModal({
                    title: 'Network Error',
                    message: `Could not create event: ${error.message}. Please check your connection and try again.`
                });
            } finally {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-plus"></i> Create Event';
            }
        });

        document.addEventListener('DOMContentLoaded', async function() {
            try {
                const token = getToken();

                if (!token) {
                    throw new Error('No authentication token found');
                }

                const response = await fetch('/api/auth/admin/verify', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    const data = await response.json();
                    throw new Error(data.message || 'Authentication failed');
                }

                const data = await response.json();

                initializeNavbar('admin', 'events');
                
                const mainContent = document.querySelector('.main-content');
                if (mainContent) {
                    mainContent.classList.add('show');
                }
                document.getElementById('auth-error').style.display = 'none';
                
                loadEvents();

            } catch (error) {
                document.getElementById('auth-error').style.display = 'block';
                
                if (error.message.includes('token')) {
                    localStorage.removeItem('token');
                }
            }
        });

        // Set minimum date to today
        document.getElementById('date').min = new Date().toISOString().split('T')[0];

        // Pagination event listeners
        document.getElementById('prevEventsBtn').addEventListener('click', () => {
            if (currentEventsPage > 1) {
                currentEventsPage--;
                displayEvents();
            }
        });

        document.getElementById('nextEventsBtn').addEventListener('click', () => {
            const totalPages = Math.ceil(allEvents.length / eventsPerPage);
            if (currentEventsPage < totalPages) {
                currentEventsPage++;
                displayEvents();
            }
        });
    </script>
    <script src="/utils/modals.js"></script>
    <script src="/utils/navbar.js"></script>
</body>
</html>
